﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.Remoting;
using System.Threading;
using CowpilotCapture.Encryption;
using CowpilotCapture.Packets;
using CowpilotCapture.Processors;
using CowpilotCaptureHook;
using EasyHook;

namespace CowpilotCapture
{
    internal class Program
    {
        private static readonly byte[] Xor3Key = new byte[] { 0xFC, 0xCF, 0xAB };

        private static HookInterface _hookInterface;
        private static string _channelName;
        private static Process _targetProcess;
        private static PacketCrypto crypto;
        private static Dictionary<int, string> itemIdsToNames;

        static void Main(string[] args)
        {
            Console.WriteLine("=== Mu Online Season 3 Packet Hooker ===");
            Console.WriteLine("Security Testing Tool - Use Responsibly");
            Console.WriteLine();

            Console.WriteLine("Reading game items");
            var itemProcessor = new ItemBmdProcessor();
            itemIdsToNames = itemProcessor.ProcessItemBmdFile("Item.bmd", Xor3Key);
            Console.WriteLine($"Read {itemIdsToNames.Count} items");
            Console.WriteLine();

            crypto = PacketCrypto.CreateForClient();

            try
            {
                // Find Mu Online process
                var muProcesses = Process
                    .GetProcesses()
                    .Where(p =>
                        p.ProcessName.ToLower().Contains("play")
                        || p.ProcessName.ToLower().Contains("bless")
                    )
                    .ToArray();

                if (!muProcesses.Any())
                {
                    Console.WriteLine("Mu Online process not found. Please start the game first.");
                    Console.WriteLine("Press any key to exit...");
                    Console.ReadKey();
                    return;
                }

                if (muProcesses.Length > 1)
                {
                    Console.WriteLine("Multiple potential Mu Online processes found:");
                    for (int i = 0; i < muProcesses.Length; i++)
                    {
                        Console.WriteLine(
                            $"{i + 1}. {muProcesses[i].ProcessName} (PID: {muProcesses[i].Id})"
                        );
                    }
                    Console.Write("Select process (1-{0}): ", muProcesses.Length);

                    if (
                        !int.TryParse(Console.ReadLine(), out int selection)
                        || selection < 1
                        || selection > muProcesses.Length
                    )
                    {
                        Console.WriteLine("Invalid selection.");
                        return;
                    }
                    _targetProcess = muProcesses[selection - 1];
                }
                else
                {
                    _targetProcess = muProcesses[0];
                }

                Console.WriteLine(
                    $"Target Process: {_targetProcess.ProcessName} (PID: {_targetProcess.Id})"
                );

                // Setup IPC
                _channelName = null;
                _hookInterface = new HookInterface();
                RemoteHooking.IpcCreateServer<HookInterface>(
                    ref _channelName,
                    WellKnownObjectMode.Singleton,
                    _hookInterface
                );

                _hookInterface.Sent += HandlePacketSent;
                _hookInterface.Received += HandlePacketReceived;

                // Inject hook DLL
                string hookDllPath = Path.Combine(
                    Path.GetDirectoryName(typeof(Program).Assembly.Location),
                    "CowpilotCaptureHook.dll"
                );

                if (!File.Exists(hookDllPath))
                {
                    Console.WriteLine($"Hook DLL not found: {hookDllPath}");
                    Console.WriteLine(
                        "Please ensure CowpilotCaptureHook.dll is in the same directory."
                    );
                    return;
                }

                RemoteHooking.Inject(_targetProcess.Id, hookDllPath, hookDllPath, _channelName);

                Console.WriteLine("Hook injected successfully!");
                Console.WriteLine();
                Console.WriteLine("Commands:");
                Console.WriteLine("  help     - Show this help");
                Console.WriteLine("  send     - Send custom packet (hex format)");
                Console.WriteLine("  sockets  - Show active socket information");
                Console.WriteLine("  status   - Show hook status");
                Console.WriteLine("  exit     - Exit application");
                Console.WriteLine();

                // Command loop
                ProcessCommands();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Details: {ex}");
            }
            finally
            {
                Cleanup();
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static void HandlePacketSent(object sender, PacketArgs e)
        {
            //var bytes = crypto.DecryptClientToServer(e.Packet);
            //Console.WriteLine($"[DEC SENT]: {BitConverter.ToString(bytes)}");
        }

        private static void HandlePacketReceived(object sender, PacketArgs e)
        {
            //Console.WriteLine($"[RAW RECV]: {BitConverter.ToString(e.Packet)}");
            //var bytes = crypto.DecryptServerToClient(e.Packet);
            //Console.WriteLine($"[DEC RECV]: {BitConverter.ToString(bytes)}");
            if (e.Packet[0] == 0xC2 && e.Packet[3] == 0x20)
            {
                Console.WriteLine($"[DROP PACKET]: {BitConverter.ToString(e.Packet)}");
                var itemsDroppedPacket = new ItemsDropped(e.Packet);

                foreach (var item in itemsDroppedPacket.Items)
                {
                    var itemName = itemIdsToNames[item.ItemData.Id];
                    Console.WriteLine($"[ITEM DROP]: {itemName} +{item.ItemData.Level}");
                }
            }
        }

        private static void ProcessCommands()
        {
            while (true)
            {
                Console.Write("> ");
                string input = Console.ReadLine()?.Trim().ToLower();

                if (string.IsNullOrEmpty(input))
                    continue;

                string[] parts = input.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                string command = parts[0];

                switch (command)
                {
                    case "help":
                        ShowHelp();
                        break;

                    case "send":
                        HandleSendCommand(parts);
                        break;

                    case "sockets":
                        ShowSocketInfo();
                        break;

                    case "status":
                        ShowStatus();
                        break;

                    case "exit":
                    case "quit":
                        Console.WriteLine("Exiting...");
                        return;

                    default:
                        Console.WriteLine(
                            $"Unknown command: {command}. Type 'help' for available commands."
                        );
                        break;
                }
            }
        }

        private static void ShowHelp()
        {
            Console.WriteLine();
            Console.WriteLine("Available Commands:");
            Console.WriteLine("  help                    - Show this help");
            Console.WriteLine(
                "  send <hex_data>        - Send custom packet (e.g., send C1 04 00 01)"
            );
            Console.WriteLine("  sockets                - Show active socket information");
            Console.WriteLine("  status                 - Show hook status");
            Console.WriteLine("  exit                   - Exit application");
            Console.WriteLine();
            Console.WriteLine("Packet Format:");
            Console.WriteLine("  Hex bytes separated by spaces");
            Console.WriteLine("  Example: C1 04 00 01 (4 bytes)");
            Console.WriteLine();
            Console.WriteLine("Notes:");
            Console.WriteLine("  - Ensure game is connected to server before sending packets");
            Console.WriteLine("  - Invalid packets may cause disconnection or crashes");
            Console.WriteLine("  - Monitor both sent and received packets for responses");
            Console.WriteLine();
        }

        private static void HandleSendCommand(string[] parts)
        {
            if (parts.Length < 2)
            {
                Console.WriteLine("Usage: send <hex_data>");
                Console.WriteLine("Example: send C1 04 00 01");
                return;
            }

            try
            {
                // Parse hex data
                var hexParts = string.Join(" ", parts.Skip(1))
                    .Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                byte[] packetData = new byte[hexParts.Length];

                for (int i = 0; i < hexParts.Length; i++)
                {
                    if (
                        !byte.TryParse(
                            hexParts[i],
                            System.Globalization.NumberStyles.HexNumber,
                            null,
                            out packetData[i]
                        )
                    )
                    {
                        Console.WriteLine($"Invalid hex byte: {hexParts[i]}");
                        return;
                    }
                }

                Console.WriteLine(
                    $"Attempting to send packet: {BitConverter.ToString(packetData)}"
                );

                // Send the custom packet through IPC
                int result = _hookInterface.SendCustomPacket(packetData);

                if (result == -1)
                {
                    Console.WriteLine("Failed to send packet. Possible reasons:");
                    Console.WriteLine("  - No active socket connection found");
                    Console.WriteLine("  - Game connection is not established");
                    Console.WriteLine("  - Socket handle is invalid");
                }
                else
                {
                    Console.WriteLine($"Packet sent successfully. Bytes sent: {result}");
                }
            }
            catch (FormatException)
            {
                Console.WriteLine("Invalid hex format. Use format like: C1 04 00 01");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending packet: {ex.Message}");
            }
        }

        private static void ShowSocketInfo()
        {
            try
            {
                int socketCount = _hookInterface.GetActiveSocketCount();
                Console.WriteLine($"Active Sockets: {socketCount}");

                if (socketCount == 0)
                {
                    Console.WriteLine(
                        "No active sockets found. Make sure the game is connected to the server."
                    );
                }
                else
                {
                    Console.WriteLine("Socket handles are being tracked for packet injection.");
                    Console.WriteLine(
                        "Most recently active socket will be used for custom packets."
                    );
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting socket information: {ex.Message}");
            }
        }

        private static void ShowStatus()
        {
            try
            {
                if (_targetProcess?.HasExited == false)
                {
                    Console.WriteLine(
                        $"Target Process: {_targetProcess.ProcessName} (PID: {_targetProcess.Id}) - Running"
                    );
                    Console.WriteLine($"Hook Status: Active");
                    Console.WriteLine($"IPC Channel: {_channelName}");
                }
                else
                {
                    Console.WriteLine("Target process has exited.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking status: {ex.Message}");
            }
        }

        private static void Cleanup()
        {
            try
            {
                _hookInterface?.RequestExit();
                Thread.Sleep(1000); // Give time for cleanup
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Cleanup error: {ex.Message}");
            }
        }
    }
}
