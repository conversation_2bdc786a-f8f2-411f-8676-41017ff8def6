{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\Code\\CowpilotCapture\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapturehook\\hookinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\hookinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapturehook\\hookentrypoint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\hookentrypoint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapture\\processors\\itembmdprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\processors\\itembmdprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapture\\packages.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\packages.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapturehook\\packetargs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\packetargs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapturehook\\socketinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\socketinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 246, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Program.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Program.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Program.cs", "RelativeToolTip": "CowpilotCapture\\Program.cs", "ViewState": "AgIAAIIAAAAAAAAAAAAqwJcAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T20:25:25.101Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "packages.config", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\packages.config", "RelativeDocumentMoniker": "CowpilotCapture\\packages.config", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\packages.config", "RelativeToolTip": "CowpilotCapture\\packages.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-07-24T20:25:24.497Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ItemBmdProcessor.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Processors\\ItemBmdProcessor.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Processors\\ItemBmdProcessor.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Processors\\ItemBmdProcessor.cs", "RelativeToolTip": "CowpilotCapture\\Processors\\ItemBmdProcessor.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAMwFwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T20:25:04.891Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "PacketArgs.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\PacketArgs.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\PacketArgs.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\PacketArgs.cs", "RelativeToolTip": "CowpilotCaptureHook\\PacketArgs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:57:22.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "HookInterface.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookInterface.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\HookInterface.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookInterface.cs", "RelativeToolTip": "CowpilotCaptureHook\\HookInterface.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAwCcAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T16:46:28.17Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "HookEntryPoint.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookEntryPoint.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\HookEntryPoint.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookEntryPoint.cs", "RelativeToolTip": "CowpilotCaptureHook\\HookEntryPoint.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAqwGQAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T16:43:43.71Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SocketInfo.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\SocketInfo.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\SocketInfo.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\SocketInfo.cs", "RelativeToolTip": "CowpilotCaptureHook\\SocketInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T16:47:50.429Z"}]}]}]}