﻿using System;

namespace CowpilotCaptureHook
{
    public class HookInterface : MarshalByRefObject
    {
        private bool _shouldExit = false;

        public event EventHandler<PacketArgs> Sent;
        public event EventHandler<PacketArgs> Received;

        protected virtual void OnSent(byte[] packet)
        {
            Sent?.Invoke(this, new PacketArgs(packet));
        }

        protected virtual void OnReceived(byte[] packet)
        {
            Received?.Invoke(this, new PacketArgs(packet));
        }

        public void Ping()
        {
            // Connection test
        }

        public void IsInstalled(int processId)
        {
            Console.WriteLine($"Hook installed in process {processId}");
        }

        public void OnPacketSent(byte[] data)
        {
            //Console.WriteLine($"[SEND] {data.Length} bytes: {BitConverter.ToString(data)}");
            OnSent(data);
        }

        public void OnPacketReceived(byte[] data)
        {
            Console.WriteLine($"[RECV] {data.Length} bytes: {BitConverter.ToString(data)}");
            OnReceived(data);
        }

        public void ReportException(Exception ex)
        {
            Console.WriteLine($"Hook Exception: {ex}");
        }

        public bool ShouldExit()
        {
            return _shouldExit;
        }

        public void RequestExit()
        {
            _shouldExit = true;
        }

        public int SendCustomPacket(byte[] packetData)
        {
            try
            {
                int result = HookEntryPoint.SendCustomPacket(packetData);
                if (result == -1)
                {
                    Console.WriteLine(
                        $"[ERROR] Failed to send custom packet: {BitConverter.ToString(packetData)}"
                    );
                    return -1;
                }

                Console.WriteLine(
                    $"[CUSTOM SEND] {packetData.Length} bytes sent: {BitConverter.ToString(packetData)}"
                );
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Exception sending custom packet: {ex.Message}");
                return -1;
            }
        }

        public int GetActiveSocketCount()
        {
            // This would require exposing the socket count from InjectionEntryPoint
            // For now, we'll return a placeholder
            return 1;
        }
    }
}
